import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { storage, generateId } from '@/lib/utils';

// Types
export interface Task {
  id: string;
  title: string;
  type: 'medication' | 'exercise' | 'diet' | 'sleep';
  completed: boolean;
  points: number;
  patientId: string;
  createdAt: string;
  completedAt?: string;
}

export interface Patient {
  id: string;
  name: string;
  email: string;
  username: string;
  password: string;
  condition: string;
  progress: number;
  streak: number;
  status: 'excellent' | 'good' | 'needs-attention';
  lastActive: string;
  totalPoints: number;
  completedTasks: number;
  doctorId: string;
  createdAt: string;
  dateOfBirth?: string;
  phone?: string;
  emergencyContact?: string;
  medicalHistory?: string;
  currentMedications?: string[];
}

export interface Doctor {
  id: string;
  name: string;
  email: string;
  username: string;
  password: string;
  patients: string[];
  specialization?: string;
  licenseNumber?: string;
  createdAt: string;
}

export interface Notification {
  id: string;
  type: 'task_completed' | 'message' | 'alert' | 'achievement';
  title: string;
  message: string;
  userId: string;
  read: boolean;
  createdAt: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
}

export interface AppState {
  currentUser: { type: 'patient' | 'doctor'; id: string; name: string } | null;
  patients: Patient[];
  doctors: Doctor[];
  tasks: Task[];
  notifications: Notification[];
  messages: Message[];
  isLoading: boolean;
}

// Actions
type AppAction =
  | { type: 'SET_USER'; payload: AppState['currentUser'] }
  | { type: 'ADD_PATIENT'; payload: Patient }
  | { type: 'UPDATE_PATIENT'; payload: { id: string; updates: Partial<Patient> } }
  | { type: 'DELETE_PATIENT'; payload: string }
  | { type: 'ADD_DOCTOR'; payload: Doctor }
  | { type: 'UPDATE_DOCTOR'; payload: { id: string; updates: Partial<Doctor> } }
  | { type: 'DELETE_DOCTOR'; payload: string }
  | { type: 'ADD_TASK'; payload: Task }
  | { type: 'UPDATE_TASK'; payload: { id: string; updates: Partial<Task> } }
  | { type: 'DELETE_TASK'; payload: string }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'MARK_MESSAGE_READ'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'LOAD_DATA'; payload: Partial<AppState> };

// Initial state
const initialState: AppState = {
  currentUser: null,
  patients: [],
  doctors: [],
  tasks: [],
  notifications: [],
  messages: [],
  isLoading: false,
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, currentUser: action.payload };
    
    case 'ADD_PATIENT':
      return { ...state, patients: [...state.patients, action.payload] };
    
    case 'UPDATE_PATIENT':
      return {
        ...state,
        patients: state.patients.map(patient =>
          patient.id === action.payload.id
            ? { ...patient, ...action.payload.updates }
            : patient
        ),
      };

    case 'DELETE_PATIENT':
      return {
        ...state,
        patients: state.patients.filter(patient => patient.id !== action.payload),
        tasks: state.tasks.filter(task => task.patientId !== action.payload),
        messages: state.messages.filter(message =>
          message.senderId !== action.payload && message.receiverId !== action.payload
        ),
        notifications: state.notifications.filter(notification =>
          notification.userId !== action.payload
        ),
      };

    case 'ADD_DOCTOR':
      return { ...state, doctors: [...state.doctors, action.payload] };

    case 'UPDATE_DOCTOR':
      return {
        ...state,
        doctors: state.doctors.map(doctor =>
          doctor.id === action.payload.id
            ? { ...doctor, ...action.payload.updates }
            : doctor
        ),
      };

    case 'DELETE_DOCTOR':
      return {
        ...state,
        doctors: state.doctors.filter(doctor => doctor.id !== action.payload),
      };
    
    case 'ADD_TASK':
      return { ...state, tasks: [...state.tasks, action.payload] };
    
    case 'UPDATE_TASK':
      return {
        ...state,
        tasks: state.tasks.map(task =>
          task.id === action.payload.id
            ? { ...task, ...action.payload.updates }
            : task
        ),
      };

    case 'DELETE_TASK':
      return {
        ...state,
        tasks: state.tasks.filter(task => task.id !== action.payload),
      };
    
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [action.payload, ...state.notifications] };
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        ),
      };
    
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    
    case 'MARK_MESSAGE_READ':
      return {
        ...state,
        messages: state.messages.map(message =>
          message.id === action.payload
            ? { ...message, read: true }
            : message
        ),
      };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'LOAD_DATA':
      return { ...state, ...action.payload };
    
    default:
      return state;
  }
};

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = {
      patients: storage.get<Patient[]>('patients', []),
      doctors: storage.get<Doctor[]>('doctors', []),
      tasks: storage.get<Task[]>('tasks', []),
      notifications: storage.get<Notification[]>('notifications', []),
      messages: storage.get<Message[]>('messages', []),
    };
    
    dispatch({ type: 'LOAD_DATA', payload: savedData });
  }, []);

  // Save data to localStorage when state changes
  useEffect(() => {
    storage.set('patients', state.patients);
    storage.set('doctors', state.doctors);
    storage.set('tasks', state.tasks);
    storage.set('notifications', state.notifications);
    storage.set('messages', state.messages);
  }, [state.patients, state.doctors, state.tasks, state.notifications, state.messages]);

  // Listen for storage changes (cross-tab sync)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key && e.newValue) {
        try {
          const newValue = JSON.parse(e.newValue);
          switch (e.key) {
            case 'patients':
              dispatch({ type: 'LOAD_DATA', payload: { patients: newValue } });
              break;
            case 'doctors':
              dispatch({ type: 'LOAD_DATA', payload: { doctors: newValue } });
              break;
            case 'tasks':
              dispatch({ type: 'LOAD_DATA', payload: { tasks: newValue } });
              break;
            case 'notifications':
              dispatch({ type: 'LOAD_DATA', payload: { notifications: newValue } });
              break;
            case 'messages':
              dispatch({ type: 'LOAD_DATA', payload: { messages: newValue } });
              break;
          }
        } catch (error) {
          console.error('Failed to parse storage data:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

// Hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
