import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useDoctor } from '@/hooks/useDoctor';
import { useToast } from '@/hooks/use-toast';
import { 
  UserPlus, 
  Trash2, 
  Eye, 
  X, 
  AlertTriangle,
  CheckCircle,
  User,
  Mail,
  Phone,
  Calendar,
  Activity
} from 'lucide-react';
import type { Patient } from '@/contexts/AppContext';

interface UserManagementProps {
  onUserChange?: () => void;
}

const UserManagement = ({ onUserChange }: UserManagementProps) => {
  const { addPatient, deletePatient, getDoctorPatients, getPatientDetails } = useDoctor();
  const { toast } = useToast();
  
  const [showAddUser, setShowAddUser] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const patients = getDoctorPatients();

  const handleAddUser = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      
      const patientData = {
        name: formData.get('name') as string,
        username: formData.get('username') as string,
        email: formData.get('email') as string,
        password: formData.get('password') as string,
        condition: formData.get('condition') as string,
        dateOfBirth: formData.get('dateOfBirth') as string,
        phone: formData.get('phone') as string,
        emergencyContact: formData.get('emergencyContact') as string,
        medicalHistory: formData.get('medicalHistory') as string,
        currentMedications: (formData.get('medications') as string)?.split(',').map(m => m.trim()).filter(Boolean) || [],
        progress: 0,
        streak: 0,
        status: 'good' as const,
        totalPoints: 0,
        completedTasks: 0,
      };

      // Validate required fields
      if (!patientData.name || !patientData.username || !patientData.email || !patientData.password) {
        throw new Error('Please fill in all required fields');
      }

      // Check if username/email already exists
      const existingPatient = patients.find(p => 
        p.username === patientData.username || p.email === patientData.email
      );
      
      if (existingPatient) {
        throw new Error('Username or email already exists');
      }

      addPatient(patientData);
      setShowAddUser(false);
      onUserChange?.();
      
      toast({
        title: "Patient Account Created",
        description: `Account for ${patientData.name} has been created successfully.`,
      });
      
      // Reset form
      (e.target as HTMLFormElement).reset();
    } catch (error) {
      toast({
        title: "Failed to Create Account",
        description: error instanceof Error ? error.message : "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = (patientId: string) => {
    if (showDeleteConfirm !== patientId) {
      setShowDeleteConfirm(patientId);
      return;
    }

    setIsLoading(true);
    try {
      const patient = patients.find(p => p.id === patientId);
      deletePatient(patientId);
      onUserChange?.();
      
      toast({
        title: "Patient Account Deleted",
        description: `Account for ${patient?.name} has been deleted.`,
      });
      
      setShowDeleteConfirm(null);
    } catch (error) {
      toast({
        title: "Failed to Delete Account",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (patientId: string) => {
    setSelectedPatient(patientId);
  };

  const patientDetails = selectedPatient ? getPatientDetails(selectedPatient) : null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">User Account Management</h2>
          <p className="text-gray-600">Create, manage, and delete patient accounts</p>
        </div>
        <Button onClick={() => setShowAddUser(true)} className="bg-green-600 hover:bg-green-700">
          <UserPlus className="h-4 w-4 mr-2" />
          Create New Account
        </Button>
      </div>

      {/* Patient List */}
      <Card>
        <CardHeader>
          <CardTitle>Patient Accounts ({patients.length})</CardTitle>
          <CardDescription>
            Manage all patient accounts under your care
          </CardDescription>
        </CardHeader>
        <CardContent>
          {patients.length > 0 ? (
            <div className="space-y-4">
              {patients.map((patient) => (
                <div key={patient.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{patient.name}</h3>
                        <p className="text-sm text-gray-600">@{patient.username}</p>
                        <p className="text-sm text-gray-600">{patient.email}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={
                            patient.status === 'excellent' ? 'bg-green-100 text-green-800' :
                            patient.status === 'good' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }>
                            {patient.status}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            Created: {new Date(patient.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewDetails(patient.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm"
                        onClick={() => handleDeleteUser(patient.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                  
                  {showDeleteConfirm === patient.id && (
                    <Alert variant="destructive" className="mt-3">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Are you sure you want to delete {patient.name}'s account? This will permanently delete all their data, tasks, and messages.
                        <div className="flex gap-2 mt-2">
                          <Button 
                            size="sm" 
                            variant="destructive"
                            onClick={() => handleDeleteUser(patient.id)}
                            disabled={isLoading}
                          >
                            Yes, Delete Account
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setShowDeleteConfirm(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No patient accounts yet</p>
              <p className="text-sm mt-1">Create your first patient account to get started</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add User Modal */}
      {showAddUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Create New Patient Account</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setShowAddUser(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <CardDescription>
                Fill in the patient's information to create their account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddUser} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input id="name" name="name" required />
                  </div>
                  <div>
                    <Label htmlFor="username">Username *</Label>
                    <Input id="username" name="username" required />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input id="email" name="email" type="email" required />
                  </div>
                  <div>
                    <Label htmlFor="password">Password *</Label>
                    <Input id="password" name="password" type="password" required />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="condition">Medical Condition *</Label>
                  <Input id="condition" name="condition" required />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Input id="dateOfBirth" name="dateOfBirth" type="date" />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" name="phone" type="tel" />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="emergencyContact">Emergency Contact</Label>
                  <Input id="emergencyContact" name="emergencyContact" />
                </div>
                
                <div>
                  <Label htmlFor="medicalHistory">Medical History</Label>
                  <Textarea id="medicalHistory" name="medicalHistory" rows={3} />
                </div>
                
                <div>
                  <Label htmlFor="medications">Current Medications (comma-separated)</Label>
                  <Input id="medications" name="medications" placeholder="e.g., Aspirin, Metformin, Lisinopril" />
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button type="submit" className="flex-1" disabled={isLoading}>
                    {isLoading ? 'Creating...' : 'Create Account'}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddUser(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Patient Details Modal */}
      {selectedPatient && patientDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Patient Details - {patientDetails.name}</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setSelectedPatient(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-3">Basic Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{patientDetails.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Username:</span>
                      <span className="font-medium">@{patientDetails.username}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{patientDetails.email}</span>
                    </div>
                    {patientDetails.phone && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Phone:</span>
                        <span className="font-medium">{patientDetails.phone}</span>
                      </div>
                    )}
                    {patientDetails.dateOfBirth && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date of Birth:</span>
                        <span className="font-medium">{new Date(patientDetails.dateOfBirth).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-3">Medical Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Condition:</span>
                      <span className="font-medium">{patientDetails.condition}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge className={
                        patientDetails.status === 'excellent' ? 'bg-green-100 text-green-800' :
                        patientDetails.status === 'good' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }>
                        {patientDetails.status}
                      </Badge>
                    </div>
                    {patientDetails.emergencyContact && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Emergency Contact:</span>
                        <span className="font-medium">{patientDetails.emergencyContact}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Progress Stats */}
              <div>
                <h3 className="font-semibold mb-3">Progress Statistics</h3>
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{patientDetails.taskStats.completed}</div>
                    <div className="text-sm text-gray-600">Tasks Completed</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{patientDetails.taskStats.completionRate}%</div>
                    <div className="text-sm text-gray-600">Completion Rate</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{patientDetails.taskStats.totalPoints}</div>
                    <div className="text-sm text-gray-600">Total Points</div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{patientDetails.taskStats.currentStreak}</div>
                    <div className="text-sm text-gray-600">Current Streak</div>
                  </div>
                </div>
              </div>

              {/* Medical History & Medications */}
              {(patientDetails.medicalHistory || patientDetails.currentMedications?.length) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {patientDetails.medicalHistory && (
                    <div>
                      <h3 className="font-semibold mb-3">Medical History</h3>
                      <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">{patientDetails.medicalHistory}</p>
                    </div>
                  )}
                  
                  {patientDetails.currentMedications?.length && (
                    <div>
                      <h3 className="font-semibold mb-3">Current Medications</h3>
                      <div className="space-y-2">
                        {patientDetails.currentMedications.map((medication, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>{medication}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Recent Activity */}
              {patientDetails.recentActivity.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-3">Recent Activity</h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {patientDetails.recentActivity.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm">{task.title}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {task.completedAt && new Date(task.completedAt).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
