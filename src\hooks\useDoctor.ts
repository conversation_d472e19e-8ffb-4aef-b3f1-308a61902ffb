import { useApp } from '@/contexts/AppContext';
import { generateId } from '@/lib/utils';
import type { Patient, Doctor, Task, Message, Notification } from '@/contexts/AppContext';

export const useDoctor = () => {
  const { state, dispatch } = useApp();

  const getCurrentDoctor = (): Doctor | null => {
    if (!state.currentUser || state.currentUser.type !== 'doctor') return null;
    return state.doctors.find(d => d.id === state.currentUser!.id) || null;
  };

  const getDoctorPatients = (doctorId?: string): Patient[] => {
    const id = doctorId || state.currentUser?.id;
    if (!id) return [];
    const doctor = state.doctors.find(d => d.id === id);
    if (!doctor) return [];
    return state.patients.filter(patient => doctor.patients.includes(patient.id));
  };

  const addPatient = (patientData: Omit<Patient, 'id' | 'doctorId'>): void => {
    const doctorId = state.currentUser?.id;
    if (!doctorId) return;

    const patient: Patient = {
      ...patientData,
      id: generateId(),
      doctorId,
      lastActive: new Date().toISOString()
    };

    dispatch({ type: 'ADD_PATIENT', payload: patient });

    // Update doctor's patient list
    const doctor = getCurrentDoctor();
    if (doctor) {
      const updatedDoctor = {
        ...doctor,
        patients: [...doctor.patients, patient.id]
      };
      // Note: We'd need to add UPDATE_DOCTOR action to the reducer
      // For now, we'll handle this in the patient addition
    }

    // Create default daily tasks for the new patient
    const defaultTasks = [
      { title: 'Take morning medication', type: 'medication' as const, points: 50 },
      { title: '30-minute walk', type: 'exercise' as const, points: 75 },
      { title: 'Eat healthy lunch', type: 'diet' as const, points: 25 },
      { title: '8 hours of sleep', type: 'sleep' as const, points: 100 },
    ];

    defaultTasks.forEach(taskData => {
      const task: Task = {
        id: generateId(),
        title: taskData.title,
        type: taskData.type,
        completed: false,
        points: taskData.points,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      };
      dispatch({ type: 'ADD_TASK', payload: task });
    });
  };

  const updatePatient = (patientId: string, updates: Partial<Patient>): void => {
    dispatch({
      type: 'UPDATE_PATIENT',
      payload: { id: patientId, updates }
    });
  };

  const sendMessage = (receiverId: string, content: string): void => {
    const senderId = state.currentUser?.id;
    if (!senderId) return;

    const message: Message = {
      id: generateId(),
      senderId,
      receiverId,
      content,
      timestamp: new Date().toISOString(),
      read: false
    };

    dispatch({ type: 'ADD_MESSAGE', payload: message });

    // Create notification for receiver
    const receiver = state.patients.find(p => p.id === receiverId) || 
                    state.doctors.find(d => d.id === receiverId);
    
    if (receiver) {
      const notification: Notification = {
        id: generateId(),
        type: 'message',
        title: 'New Message',
        message: `New message from ${state.currentUser.name}`,
        userId: receiverId,
        read: false,
        createdAt: new Date().toISOString()
      };
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    }
  };

  const getPatientMessages = (patientId: string): Message[] => {
    const doctorId = state.currentUser?.id;
    if (!doctorId) return [];

    return state.messages.filter(message => 
      (message.senderId === doctorId && message.receiverId === patientId) ||
      (message.senderId === patientId && message.receiverId === doctorId)
    ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  };

  const getDoctorStats = (doctorId?: string) => {
    const id = doctorId || state.currentUser?.id;
    if (!id) return { totalPatients: 0, activePatients: 0, completionRate: 0 };

    const patients = getDoctorPatients(id);
    const totalPatients = patients.length;
    
    // Active patients (active in last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const activePatients = patients.filter(patient => 
      new Date(patient.lastActive) >= yesterday
    ).length;

    // Overall completion rate
    const allTasks = state.tasks.filter(task => 
      patients.some(patient => patient.id === task.patientId)
    );
    const completedTasks = allTasks.filter(task => task.completed);
    const completionRate = allTasks.length > 0 
      ? Math.round((completedTasks.length / allTasks.length) * 100)
      : 0;

    return { totalPatients, activePatients, completionRate };
  };

  const searchPatients = (query: string): Patient[] => {
    const patients = getDoctorPatients();
    if (!query.trim()) return patients;

    const lowercaseQuery = query.toLowerCase();
    return patients.filter(patient =>
      patient.name.toLowerCase().includes(lowercaseQuery) ||
      patient.condition.toLowerCase().includes(lowercaseQuery)
    );
  };

  const filterPatients = (status?: string): Patient[] => {
    const patients = getDoctorPatients();
    if (!status || status === 'all') return patients;
    return patients.filter(patient => patient.status === status);
  };

  const getPatientTasks = (patientId: string): Task[] => {
    return state.tasks.filter(task => task.patientId === patientId);
  };

  const assignTaskToPatient = (patientId: string, taskData: Omit<Task, 'id' | 'patientId' | 'createdAt'>): void => {
    const task: Task = {
      ...taskData,
      id: generateId(),
      patientId,
      createdAt: new Date().toISOString()
    };
    dispatch({ type: 'ADD_TASK', payload: task });

    // Notify patient
    const notification: Notification = {
      id: generateId(),
      type: 'alert',
      title: 'New Task Assigned',
      message: `Your doctor assigned you a new task: ${task.title}`,
      userId: patientId,
      read: false,
      createdAt: new Date().toISOString()
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
  };

  return {
    getCurrentDoctor,
    getDoctorPatients,
    addPatient,
    updatePatient,
    sendMessage,
    getPatientMessages,
    getDoctorStats,
    searchPatients,
    filterPatients,
    getPatientTasks,
    assignTaskToPatient,
    doctors: state.doctors,
    patients: state.patients,
    messages: state.messages
  };
};
