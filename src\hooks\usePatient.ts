import { useApp } from '@/contexts/AppContext';
import { generateId } from '@/lib/utils';
import type { Task, Patient, Notification } from '@/contexts/AppContext';

export const usePatient = () => {
  const { state, dispatch } = useApp();

  const getCurrentPatient = (): Patient | null => {
    if (!state.currentUser || state.currentUser.type !== 'patient') return null;
    return state.patients.find(p => p.id === state.currentUser!.id) || null;
  };

  const getPatientTasks = (patientId?: string): Task[] => {
    const id = patientId || state.currentUser?.id;
    if (!id) return [];
    return state.tasks.filter(task => task.patientId === id);
  };

  const getTodayTasks = (patientId?: string): Task[] => {
    const tasks = getPatientTasks(patientId);
    const today = new Date().toDateString();
    return tasks.filter(task => new Date(task.createdAt).toDateString() === today);
  };

  const completeTask = async (taskId: string): Promise<void> => {
    const task = state.tasks.find(t => t.id === taskId);
    if (!task || task.completed) return;

    const completedAt = new Date().toISOString();
    
    // Update task
    dispatch({
      type: 'UPDATE_TASK',
      payload: {
        id: taskId,
        updates: { completed: true, completedAt }
      }
    });

    // Update patient stats
    const patient = state.patients.find(p => p.id === task.patientId);
    if (patient) {
      const newTotalPoints = patient.totalPoints + task.points;
      const newCompletedTasks = patient.completedTasks + 1;
      
      // Calculate accurate streak
      const allPatientTasks = state.tasks.filter(t => t.patientId === task.patientId && t.completed);

      // Group completed tasks by date
      const tasksByDate = allPatientTasks.reduce((acc, t) => {
        if (t.completedAt) {
          const date = new Date(t.completedAt);
          date.setHours(0, 0, 0, 0);
          const dateStr = date.toISOString().split('T')[0];

          if (!acc[dateStr]) acc[dateStr] = [];
          acc[dateStr].push(t);
        }
        return acc;
      }, {} as Record<string, typeof allPatientTasks>);

      // Calculate consecutive days with completed tasks
      let newStreak = 0;
      let currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      // Include today's newly completed task
      const todayStr = currentDate.toISOString().split('T')[0];
      if (!tasksByDate[todayStr]) tasksByDate[todayStr] = [];
      tasksByDate[todayStr].push(task);

      let checkDate = new Date(currentDate);

      while (true) {
        const dateStr = checkDate.toISOString().split('T')[0];

        if (tasksByDate[dateStr] && tasksByDate[dateStr].length > 0) {
          newStreak++;
          checkDate.setDate(checkDate.getDate() - 1);
        } else {
          break;
        }
      }

      dispatch({
        type: 'UPDATE_PATIENT',
        payload: {
          id: task.patientId,
          updates: {
            totalPoints: newTotalPoints,
            completedTasks: newCompletedTasks,
            streak: newStreak,
            lastActive: new Date().toISOString(),
            progress: Math.min(100, Math.round((newCompletedTasks / 50) * 100)) // Assuming 50 tasks for 100% progress
          }
        }
      });

      // Create notification for doctor
      const doctor = state.doctors.find(d => d.patients.includes(task.patientId));
      if (doctor) {
        const notification: Notification = {
          id: generateId(),
          type: 'task_completed',
          title: 'Task Completed',
          message: `${patient.name} completed: ${task.title}`,
          userId: doctor.id,
          read: false,
          createdAt: new Date().toISOString()
        };
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      }

      // Achievement notifications
      if (newStreak > 0 && newStreak % 7 === 0) {
        const achievementNotification: Notification = {
          id: generateId(),
          type: 'achievement',
          title: 'Streak Achievement!',
          message: `Congratulations! You've maintained a ${newStreak}-day streak!`,
          userId: task.patientId,
          read: false,
          createdAt: new Date().toISOString()
        };
        dispatch({ type: 'ADD_NOTIFICATION', payload: achievementNotification });
      }
    }
  };

  const addTask = (taskData: Omit<Task, 'id' | 'createdAt'>): void => {
    const task: Task = {
      ...taskData,
      id: generateId(),
      createdAt: new Date().toISOString()
    };
    dispatch({ type: 'ADD_TASK', payload: task });
  };

  const getPatientProgress = (patientId?: string) => {
    const id = patientId || state.currentUser?.id;
    if (!id) return { medication: 0, exercise: 0, diet: 0, sleep: 0 };

    const tasks = getPatientTasks(id);
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);

    const recentTasks = tasks.filter(task => 
      new Date(task.createdAt) >= last7Days
    );

    const progress = {
      medication: 0,
      exercise: 0,
      diet: 0,
      sleep: 0
    };

    Object.keys(progress).forEach(type => {
      const typeTasks = recentTasks.filter(task => task.type === type);
      const completedTypeTasks = typeTasks.filter(task => task.completed);
      progress[type as keyof typeof progress] = typeTasks.length > 0 
        ? Math.round((completedTypeTasks.length / typeTasks.length) * 100)
        : 0;
    });

    return progress;
  };

  return {
    getCurrentPatient,
    getPatientTasks,
    getTodayTasks,
    completeTask,
    addTask,
    getPatientProgress,
    patients: state.patients,
    tasks: state.tasks
  };
};
