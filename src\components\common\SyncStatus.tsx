import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useRealTimeSync } from '@/hooks/useRealTimeSync';
import { 
  Wifi, 
  WifiOff, 
  Refresh<PERSON><PERSON>, 
  CheckCircle, 
  AlertCircle 
} from 'lucide-react';

interface SyncStatusProps {
  className?: string;
}

const SyncStatus = ({ className = '' }: SyncStatusProps) => {
  const { syncData, isOnline, lastSync } = useRealTimeSync();
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<string>('');

  useEffect(() => {
    const updateLastSyncTime = () => {
      if (lastSync) {
        const date = new Date(lastSync);
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
        
        if (diffInMinutes < 1) {
          setLastSyncTime('Just now');
        } else if (diffInMinutes < 60) {
          setLastSyncTime(`${diffInMinutes}m ago`);
        } else {
          const diffInHours = Math.floor(diffInMinutes / 60);
          setLastSyncTime(`${diffInHours}h ago`);
        }
      }
    };

    updateLastSyncTime();
    const interval = setInterval(updateLastSyncTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastSync]);

  const handleManualSync = async () => {
    setIsSyncing(true);
    try {
      await syncData();
      // Simulate sync delay for better UX
      setTimeout(() => {
        setIsSyncing(false);
      }, 1000);
    } catch (error) {
      setIsSyncing(false);
    }
  };

  const getSyncIcon = () => {
    if (isSyncing) {
      return <RefreshCw className="h-3 w-3 animate-spin" />;
    }
    if (!isOnline) {
      return <WifiOff className="h-3 w-3" />;
    }
    return <CheckCircle className="h-3 w-3" />;
  };

  const getSyncStatus = () => {
    if (isSyncing) return 'Syncing...';
    if (!isOnline) return 'Offline';
    return 'Synced';
  };

  const getSyncVariant = () => {
    if (isSyncing) return 'secondary';
    if (!isOnline) return 'destructive';
    return 'default';
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge variant={getSyncVariant()} className="flex items-center gap-1 text-xs">
        {getSyncIcon()}
        {getSyncStatus()}
      </Badge>
      
      {isOnline && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualSync}
          disabled={isSyncing}
          className="h-6 px-2 text-xs"
        >
          <RefreshCw className={`h-3 w-3 mr-1 ${isSyncing ? 'animate-spin' : ''}`} />
          Sync
        </Button>
      )}
      
      {lastSyncTime && (
        <span className="text-xs text-gray-500">
          Last sync: {lastSyncTime}
        </span>
      )}
    </div>
  );
};

export default SyncStatus;
