
import { useEffect } from "react";
import { useApp } from "@/contexts/AppContext";
import { useDoctor } from "@/hooks/useDoctor";
import { initializeMockData } from "@/lib/mockData";
import { initializeAutoBackup } from "@/lib/dataBackup";
import LoginForm from "@/components/auth/LoginForm";
import PatientDashboard from "@/components/patient/PatientDashboard";
import DoctorDashboard from "@/components/doctor/DoctorDashboard";

const Index = () => {
  const { state, dispatch } = useApp();
  const { validatePatientCredentials, validateDoctorCredentials } = useDoctor();

  // Initialize mock data and auto-backup on first load
  useEffect(() => {
    initializeMockData();
    initializeAutoBackup();
  }, []);

  const handleLogin = (usernameOrEmail: string, password: string, userType: "patient" | "doctor") => {
    try {
      if (userType === "patient") {
        const patient = validatePatientCredentials(usernameOrEmail, password);
        if (patient) {
          const user = {
            type: userType,
            id: patient.id,
            name: patient.name
          };
          dispatch({ type: 'SET_USER', payload: user });
          return true;
        }
      } else if (userType === "doctor") {
        const doctor = validateDoctorCredentials(usernameOrEmail, password);
        if (doctor) {
          const user = {
            type: userType,
            id: doctor.id,
            name: doctor.name
          };
          dispatch({ type: 'SET_USER', payload: user });
          return true;
        }
      }

      // Fallback to mock users for demo purposes
      const mockUsers = {
        doctor: { email: "<EMAIL>", password: "doctor123", name: "Dr. Smith", id: "mock-doctor-id" },
        patient: { email: "<EMAIL>", password: "patient123", name: "John Doe", id: "mock-patient-id" }
      };

      const mockUser = mockUsers[userType];
      if (usernameOrEmail === mockUser.email && password === mockUser.password) {
        const user = {
          type: userType,
          id: mockUser.id,
          name: mockUser.name
        };
        dispatch({ type: 'SET_USER', payload: user });
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const handleLogout = () => {
    dispatch({ type: 'SET_USER', payload: null });
  };

  if (!state.currentUser) {
    return <LoginForm onLogin={handleLogin} />;
  }

  if (state.currentUser.type === "patient") {
    return <PatientDashboard user={state.currentUser} onLogout={handleLogout} />;
  }

  if (state.currentUser.type === "doctor") {
    return <DoctorDashboard user={state.currentUser} onLogout={handleLogout} />;
  }

  return <LoginForm onLogin={handleLogin} />;
};

export default Index;
