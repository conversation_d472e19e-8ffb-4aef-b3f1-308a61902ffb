
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  TrendingUp, 
  Calendar, 
  MessageSquare,
  Plus,
  Search,
  Filter,
  AlertCircle,
  CheckCircle,
  LogOut,
  Bell,
  Settings
} from "lucide-react";
import { Input } from "@/components/ui/input";

interface User {
  type: "patient" | "doctor";
  id: string;
  name: string;
}

interface DoctorDashboardProps {
  user: User;
  onLogout: () => void;
}

const DoctorDashboard = ({ user, onLogout }: DoctorDashboardProps) => {
  const [totalPatients] = useState(24);
  const [activePatients] = useState(18);
  const [completionRate] = useState(78);
  
  const patients = [
    { 
      id: 1, 
      name: "<PERSON>", 
      condition: "Post-Surgery Recovery", 
      progress: 85, 
      streak: 7, 
      status: "excellent",
      lastActive: "2 hours ago"
    },
    { 
      id: 2, 
      name: "<PERSON>", 
      condition: "Cardiac Rehabilitation", 
      progress: 92, 
      streak: 12, 
      status: "excellent",
      lastActive: "1 hour ago"
    },
    { 
      id: 3, 
      name: "<PERSON> <PERSON>", 
      condition: "Physical Therapy", 
      progress: 45, 
      streak: 2, 
      status: "needs-attention",
      lastActive: "1 day ago"
    },
    { 
      id: 4, 
      name: "Emma <PERSON>", 
      condition: "Diabetes Management", 
      progress: 78, 
      streak: 5, 
      status: "good",
      lastActive: "3 hours ago"
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent": return "bg-green-100 text-green-800";
      case "good": return "bg-blue-100 text-blue-800";
      case "needs-attention": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "excellent": return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "good": return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case "needs-attention": return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Welcome, {user.name}</h1>
            <p className="text-gray-600">Monitor and manage your patients' recovery</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Patient
            </Button>
            <Button variant="outline" size="sm" onClick={onLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-r from-green-400 to-green-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Total Patients</p>
                  <p className="text-2xl font-bold">{totalPatients}</p>
                </div>
                <Users className="h-8 w-8 text-green-200" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-blue-400 to-blue-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Active Today</p>
                  <p className="text-2xl font-bold">{activePatients}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-purple-400 to-purple-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Completion Rate</p>
                  <p className="text-2xl font-bold">{completionRate}%</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-orange-400 to-orange-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Messages</p>
                  <p className="text-2xl font-bold">7</p>
                </div>
                <MessageSquare className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Patient Management */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Patient Management</CardTitle>
                <CardDescription>Monitor your patients' progress and engagement</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input placeholder="Search patients..." className="pl-10 w-64" />
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {patients.map((patient) => (
                <div key={patient.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-green-400 rounded-full flex items-center justify-center text-white font-semibold">
                        {patient.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{patient.name}</h3>
                        <p className="text-sm text-gray-600">{patient.condition}</p>
                        <p className="text-xs text-gray-500">Last active: {patient.lastActive}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-6">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-700">Progress</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Progress value={patient.progress} className="w-20 h-2" />
                          <span className="text-sm font-semibold">{patient.progress}%</span>
                        </div>
                      </div>
                      
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-700">Streak</p>
                        <p className="text-lg font-bold text-orange-600">{patient.streak} days</p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {getStatusIcon(patient.status)}
                        <Badge className={getStatusColor(patient.status)}>
                          {patient.status.replace('-', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Chat
                        </Button>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DoctorDashboard;
