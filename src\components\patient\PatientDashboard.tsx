
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { usePatient } from "@/hooks/usePatient";
import { useNotifications } from "@/hooks/useNotifications";
import { useRealTimeSync } from "@/hooks/useRealTimeSync";
import { useToast } from "@/hooks/use-toast";
import NotificationCenter from "@/components/notifications/NotificationCenter";
import SyncStatus from "@/components/common/SyncStatus";
import ChatSystem from "@/components/chat/ChatSystem";
import ProgressAnalytics from "@/components/analytics/ProgressAnalytics";
import {
  Trophy,
  Target,
  Calendar,
  Award,
  Flame,
  Heart,
  Activity,
  Pill,
  Apple,
  Moon,
  LogOut,
  MessageSquare,
  Bell,
  X
} from "lucide-react";

interface User {
  type: "patient" | "doctor";
  id: string;
  name: string;
}

interface PatientDashboardProps {
  user: User;
  onLogout: () => void;
}

const PatientDashboard = ({ user, onLogout }: PatientDashboardProps) => {
  const { getCurrentPatient, getTodayTasks, completeTask, getPatientProgress } = usePatient();
  const { getUserNotifications, getUnreadCount, markAsRead } = useNotifications();
  const { syncData, isOnline } = useRealTimeSync();
  const { toast } = useToast();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);

  const currentPatient = getCurrentPatient();
  const todayTasks = getTodayTasks();
  const progress = getPatientProgress();
  const notifications = getUserNotifications();
  const unreadCount = getUnreadCount();

  const getTaskIcon = (type: string) => {
    switch (type) {
      case "medication": return <Pill className="h-4 w-4" />;
      case "exercise": return <Activity className="h-4 w-4" />;
      case "diet": return <Apple className="h-4 w-4" />;
      case "sleep": return <Moon className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const handleCompleteTask = async (taskId: string) => {
    try {
      await completeTask(taskId);
      toast({
        title: "Task Completed!",
        description: "Great job! You've earned points for completing this task.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  const handleChatClick = () => {
    setShowChat(!showChat);
  };

  const handleProfileClick = () => {
    setShowProfile(!showProfile);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Welcome back, {user.name}!</h1>
            <p className="text-gray-600">Let's continue your recovery journey</p>
            <SyncStatus className="mt-2" />
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleNotificationClick}
              className="relative"
            >
              <Bell className="h-4 w-4 mr-2" />
              Notifications
              {unreadCount > 0 && (
                <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-red-500 text-white text-xs">
                  {unreadCount}
                </Badge>
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={handleChatClick}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </Button>
            <Button variant="outline" size="sm" onClick={handleProfileClick}>
              <Heart className="h-4 w-4 mr-2" />
              Profile
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowAnalytics(!showAnalytics)}>
              <Activity className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            <Button variant="outline" size="sm" onClick={onLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-r from-orange-400 to-orange-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Current Streak</p>
                  <p className="text-2xl font-bold">{currentPatient?.streak || 0} days</p>
                </div>
                <Flame className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-400 to-blue-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Total Points</p>
                  <p className="text-2xl font-bold">{currentPatient?.totalPoints.toLocaleString() || 0}</p>
                </div>
                <Trophy className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-400 to-green-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Tasks Done</p>
                  <p className="text-2xl font-bold">{currentPatient?.completedTasks || 0}</p>
                </div>
                <Target className="h-8 w-8 text-green-200" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-purple-400 to-purple-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Level</p>
                  <p className="text-2xl font-bold">12</p>
                </div>
                <Award className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Today's Tasks */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Today's Tasks
                </CardTitle>
                <CardDescription>
                  Complete your daily health activities
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {todayTasks.length > 0 ? (
                  todayTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTaskIcon(task.type)}
                        <span className={task.completed ? "line-through text-gray-500" : ""}>
                          {task.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">+{task.points} pts</Badge>
                        <Button
                          size="sm"
                          variant={task.completed ? "default" : "outline"}
                          onClick={() => handleCompleteTask(task.id)}
                          disabled={task.completed}
                        >
                          {task.completed ? "Done" : "Mark Done"}
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No tasks for today. Great job staying on track!</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Progress & Achievements */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Weekly Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Medication</span>
                      <span>{progress.medication}%</span>
                    </div>
                    <Progress value={progress.medication} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Exercise</span>
                      <span>{progress.exercise}%</span>
                    </div>
                    <Progress value={progress.exercise} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Diet</span>
                      <span>{progress.diet}%</span>
                    </div>
                    <Progress value={progress.diet} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Sleep</span>
                      <span>{progress.sleep}%</span>
                    </div>
                    <Progress value={progress.sleep} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Badges</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  <div className="text-center p-2">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-1">
                      <Trophy className="h-6 w-6 text-yellow-600" />
                    </div>
                    <span className="text-xs">Week Master</span>
                  </div>
                  <div className="text-center p-2">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                      <Target className="h-6 w-6 text-blue-600" />
                    </div>
                    <span className="text-xs">Streak Hero</span>
                  </div>
                  <div className="text-center p-2">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-1">
                      <Heart className="h-6 w-6 text-green-600" />
                    </div>
                    <span className="text-xs">Health Star</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Notifications Panel */}
        <NotificationCenter
          isOpen={showNotifications}
          onClose={() => setShowNotifications(false)}
          userId={user.id}
        />

        {/* Chat System */}
        <ChatSystem
          isOpen={showChat}
          onClose={() => setShowChat(false)}
          currentUserId={user.id}
          currentUserType={user.type}
          recipientId={currentPatient?.doctorId}
          recipientName="Dr. Smith"
        />

        {/* Profile Panel */}
        {showProfile && (
          <Card className="fixed top-20 right-4 w-80 z-50 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Profile</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setShowProfile(false)}>
                  ×
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {currentPatient && (
                <>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Heart className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="font-semibold">{currentPatient.name}</h3>
                    <p className="text-sm text-gray-600">{currentPatient.email}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Condition:</span>
                      <span className="text-sm font-medium">{currentPatient.condition}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Progress:</span>
                      <span className="text-sm font-medium">{currentPatient.progress}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <Badge className={
                        currentPatient.status === 'excellent' ? 'bg-green-100 text-green-800' :
                        currentPatient.status === 'good' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }>
                        {currentPatient.status.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* Analytics Panel */}
        {showAnalytics && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl">Progress Analytics</CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setShowAnalytics(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <CardDescription>
                  Detailed insights into your health journey and progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProgressAnalytics patientId={user.id} timeRange="week" />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientDashboard;
