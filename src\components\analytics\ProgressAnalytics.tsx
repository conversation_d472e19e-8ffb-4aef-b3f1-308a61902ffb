import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { usePatient } from '@/hooks/usePatient';
import { useApp } from '@/contexts/AppContext';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Target,
  Award,
  Activity,
  Pill,
  Apple,
  Moon,
  BarChart3
} from 'lucide-react';

interface ProgressAnalyticsProps {
  patientId?: string;
  timeRange?: 'week' | 'month' | 'all';
}

const ProgressAnalytics = ({ patientId, timeRange = 'week' }: ProgressAnalyticsProps) => {
  const { state } = useApp();
  const { getPatientTasks, getPatientProgress } = usePatient();
  
  const currentUser = state.currentUser;
  const targetPatientId = patientId || currentUser?.id;
  
  if (!targetPatientId) return null;

  const patient = state.patients.find(p => p.id === targetPatientId);
  const tasks = getPatientTasks(targetPatientId);
  const progress = getPatientProgress(targetPatientId);

  // Calculate date range
  const now = new Date();
  const startDate = new Date();
  switch (timeRange) {
    case 'week':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      startDate.setDate(now.getDate() - 30);
      break;
    case 'all':
      startDate.setFullYear(2020); // Far back date
      break;
  }

  const filteredTasks = tasks.filter(task => 
    new Date(task.createdAt) >= startDate
  );

  const completedTasks = filteredTasks.filter(task => task.completed);
  const completionRate = filteredTasks.length > 0 
    ? Math.round((completedTasks.length / filteredTasks.length) * 100)
    : 0;

  // Calculate streaks and trends
  const calculateStreak = () => {
    const sortedTasks = tasks
      .filter(task => task.completed)
      .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime());
    
    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    
    for (const task of sortedTasks) {
      const taskDate = new Date(task.completedAt!);
      taskDate.setHours(0, 0, 0, 0);
      
      const diffDays = Math.floor((currentDate.getTime() - taskDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (diffDays === streak) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  };

  // Task type breakdown
  const taskTypeStats = {
    medication: { completed: 0, total: 0, icon: Pill, color: 'text-blue-600' },
    exercise: { completed: 0, total: 0, icon: Activity, color: 'text-green-600' },
    diet: { completed: 0, total: 0, icon: Apple, color: 'text-orange-600' },
    sleep: { completed: 0, total: 0, icon: Moon, color: 'text-purple-600' }
  };

  filteredTasks.forEach(task => {
    taskTypeStats[task.type].total++;
    if (task.completed) {
      taskTypeStats[task.type].completed++;
    }
  });

  // Points earned
  const totalPoints = completedTasks.reduce((sum, task) => sum + task.points, 0);
  const averagePointsPerTask = completedTasks.length > 0 
    ? Math.round(totalPoints / completedTasks.length)
    : 0;

  // Weekly comparison (if timeRange is week)
  const getWeeklyComparison = () => {
    if (timeRange !== 'week') return null;
    
    const lastWeekStart = new Date();
    lastWeekStart.setDate(now.getDate() - 14);
    const lastWeekEnd = new Date();
    lastWeekEnd.setDate(now.getDate() - 7);
    
    const lastWeekTasks = tasks.filter(task => {
      const taskDate = new Date(task.createdAt);
      return taskDate >= lastWeekStart && taskDate < lastWeekEnd;
    });
    
    const lastWeekCompleted = lastWeekTasks.filter(task => task.completed).length;
    const thisWeekCompleted = completedTasks.length;
    
    const change = thisWeekCompleted - lastWeekCompleted;
    const percentChange = lastWeekCompleted > 0 
      ? Math.round((change / lastWeekCompleted) * 100)
      : thisWeekCompleted > 0 ? 100 : 0;
    
    return { change, percentChange };
  };

  const weeklyComparison = getWeeklyComparison();

  const getTaskTypeIcon = (type: keyof typeof taskTypeStats) => {
    const IconComponent = taskTypeStats[type].icon;
    return <IconComponent className={`h-4 w-4 ${taskTypeStats[type].color}`} />;
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold">{completionRate}%</p>
                {weeklyComparison && (
                  <div className="flex items-center gap-1 mt-1">
                    {weeklyComparison.change >= 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-600" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-600" />
                    )}
                    <span className={`text-xs ${
                      weeklyComparison.change >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {weeklyComparison.percentChange > 0 ? '+' : ''}{weeklyComparison.percentChange}%
                    </span>
                  </div>
                )}
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tasks Completed</p>
                <p className="text-2xl font-bold">{completedTasks.length}</p>
                <p className="text-xs text-gray-500">of {filteredTasks.length} total</p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Points Earned</p>
                <p className="text-2xl font-bold">{totalPoints}</p>
                <p className="text-xs text-gray-500">avg {averagePointsPerTask}/task</p>
              </div>
              <Award className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Current Streak</p>
                <p className="text-2xl font-bold">{patient?.streak || 0}</p>
                <p className="text-xs text-gray-500">days</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Type Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Task Type Performance</CardTitle>
          <CardDescription>
            Completion rates by task category for the selected period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(taskTypeStats).map(([type, stats]) => {
              const completionRate = stats.total > 0 
                ? Math.round((stats.completed / stats.total) * 100)
                : 0;
              
              return (
                <div key={type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getTaskTypeIcon(type as keyof typeof taskTypeStats)}
                      <span className="font-medium capitalize">{type}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">
                        {stats.completed}/{stats.total}
                      </span>
                      <Badge variant={completionRate >= 80 ? 'default' : completionRate >= 60 ? 'secondary' : 'destructive'}>
                        {completionRate}%
                      </Badge>
                    </div>
                  </div>
                  <Progress value={completionRate} className="h-2" />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Weekly Progress (if applicable) */}
      {progress && (
        <Card>
          <CardHeader>
            <CardTitle>Weekly Progress Overview</CardTitle>
            <CardDescription>
              Your progress across different health categories this week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(progress).map(([category, value]) => (
                <div key={category} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium capitalize">{category}</span>
                    <span className="text-sm font-semibold">{value}%</span>
                  </div>
                  <Progress value={value} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProgressAnalytics;
