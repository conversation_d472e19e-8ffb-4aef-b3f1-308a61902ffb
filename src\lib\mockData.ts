import { generateId } from './utils';
import type { Patient, Doctor, Task } from '@/contexts/AppContext';

// Mock data initialization
export const initializeMockData = () => {
  // Check if data already exists
  const existingPatients = localStorage.getItem('patients');
  if (existingPatients && JSON.parse(existingPatients).length > 0) {
    return; // Data already exists
  }

  // Create mock doctor
  const mockDoctor: Doctor = {
    id: 'mock-doctor-id',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    username: 'drsmith',
    password: 'doctor123',
    patients: [],
    specialization: 'Internal Medicine',
    licenseNumber: 'MD123456',
    createdAt: new Date().toISOString()
  };

  // Create mock patient
  const mockPatient: Patient = {
    id: 'mock-patient-id',
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'johndo<PERSON>',
    password: 'patient123',
    condition: 'Post-Surgery Recovery',
    progress: 75,
    streak: 7,
    status: 'good',
    lastActive: new Date().toISOString(),
    totalPoints: 1250,
    completedTasks: 45,
    doctorId: mockDoctor.id,
    createdAt: new Date().toISOString(),
    dateOfBirth: '1985-06-15',
    phone: '******-0123',
    emergencyContact: 'Jane Doe - ******-0124',
    medicalHistory: 'Recent knee surgery, recovering well. No known allergies.',
    currentMedications: ['Ibuprofen 400mg', 'Physical therapy exercises']
  };

  // Update doctor's patient list
  mockDoctor.patients = [mockPatient.id];

  // Create additional mock patients for the doctor
  const additionalPatients: Patient[] = [
    {
      id: generateId(),
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      username: 'sarahj',
      password: 'sarah123',
      condition: 'Cardiac Rehabilitation',
      progress: 92,
      streak: 12,
      status: 'excellent',
      lastActive: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
      totalPoints: 2100,
      completedTasks: 78,
      doctorId: mockDoctor.id,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
      dateOfBirth: '1978-03-22',
      phone: '******-0125',
      emergencyContact: 'Robert Johnson - ******-0126',
      medicalHistory: 'Heart attack 6 months ago, excellent recovery progress.',
      currentMedications: ['Atorvastatin 20mg', 'Metoprolol 50mg', 'Aspirin 81mg']
    },
    {
      id: generateId(),
      name: 'Mike Davis',
      email: '<EMAIL>',
      username: 'miked',
      password: 'mike123',
      condition: 'Physical Therapy',
      progress: 45,
      streak: 2,
      status: 'needs-attention',
      lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      totalPoints: 680,
      completedTasks: 23,
      doctorId: mockDoctor.id,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14).toISOString(), // 14 days ago
      dateOfBirth: '1992-11-08',
      phone: '******-0127',
      emergencyContact: 'Lisa Davis - ******-0128',
      medicalHistory: 'Sports injury - torn ACL, undergoing rehabilitation.',
      currentMedications: ['Naproxen 500mg', 'Physical therapy sessions']
    },
    {
      id: generateId(),
      name: 'Emma Wilson',
      email: '<EMAIL>',
      username: 'emmaw',
      password: 'emma123',
      condition: 'Diabetes Management',
      progress: 78,
      streak: 5,
      status: 'good',
      lastActive: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      totalPoints: 1450,
      completedTasks: 52,
      doctorId: mockDoctor.id,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago
      dateOfBirth: '1965-09-14',
      phone: '******-0129',
      emergencyContact: 'David Wilson - ******-0130',
      medicalHistory: 'Type 2 diabetes diagnosed 3 years ago, well controlled.',
      currentMedications: ['Metformin 1000mg', 'Glipizide 5mg', 'Lisinopril 10mg']
    }
  ];

  // Update doctor's patient list with additional patients
  mockDoctor.patients.push(...additionalPatients.map(p => p.id));

  // Create mock tasks for the main patient
  const mockTasks: Task[] = [
    {
      id: generateId(),
      title: 'Take morning medication',
      type: 'medication',
      completed: true,
      points: 50,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: '30-minute walk',
      type: 'exercise',
      completed: true,
      points: 75,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: 'Eat healthy lunch',
      type: 'diet',
      completed: false,
      points: 25,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: '8 hours of sleep',
      type: 'sleep',
      completed: false,
      points: 100,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString()
    }
  ];

  // Create tasks for additional patients
  const additionalTasks: Task[] = [];
  additionalPatients.forEach(patient => {
    const patientTasks = [
      {
        id: generateId(),
        title: 'Take morning medication',
        type: 'medication' as const,
        completed: Math.random() > 0.3,
        points: 50,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Exercise routine',
        type: 'exercise' as const,
        completed: Math.random() > 0.4,
        points: 75,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Healthy meal',
        type: 'diet' as const,
        completed: Math.random() > 0.2,
        points: 25,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Rest and recovery',
        type: 'sleep' as const,
        completed: Math.random() > 0.5,
        points: 100,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      }
    ];
    additionalTasks.push(...patientTasks);
  });

  // Save to localStorage
  localStorage.setItem('doctors', JSON.stringify([mockDoctor]));
  localStorage.setItem('patients', JSON.stringify([mockPatient, ...additionalPatients]));
  localStorage.setItem('tasks', JSON.stringify([...mockTasks, ...additionalTasks]));
  localStorage.setItem('notifications', JSON.stringify([]));
  localStorage.setItem('messages', JSON.stringify([]));
};
