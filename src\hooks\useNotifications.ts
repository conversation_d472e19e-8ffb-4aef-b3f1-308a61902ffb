import { useApp } from '@/contexts/AppContext';
import type { Notification } from '@/contexts/AppContext';

export const useNotifications = () => {
  const { state, dispatch } = useApp();

  const getUserNotifications = (userId?: string): Notification[] => {
    const id = userId || state.currentUser?.id;
    if (!id) return [];
    
    return state.notifications
      .filter(notification => notification.userId === id)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const getUnreadCount = (userId?: string): number => {
    const notifications = getUserNotifications(userId);
    return notifications.filter(notification => !notification.read).length;
  };

  const markAsRead = (notificationId: string): void => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: notificationId });
  };

  const markAllAsRead = (userId?: string): void => {
    const notifications = getUserNotifications(userId);
    notifications.forEach(notification => {
      if (!notification.read) {
        markAsRead(notification.id);
      }
    });
  };

  const getRecentNotifications = (userId?: string, limit: number = 5): Notification[] => {
    const notifications = getUserNotifications(userId);
    return notifications.slice(0, limit);
  };

  const getNotificationsByType = (type: Notification['type'], userId?: string): Notification[] => {
    const notifications = getUserNotifications(userId);
    return notifications.filter(notification => notification.type === type);
  };

  return {
    getUserNotifications,
    getUnreadCount,
    markAsRead,
    markAllAsRead,
    getRecentNotifications,
    getNotificationsByType,
    notifications: state.notifications
  };
};
