
import { useEffect } from "react";
import { useApp } from "@/contexts/AppContext";
import { initializeMockData } from "@/lib/mockData";
import LoginForm from "@/components/auth/LoginForm";
import PatientDashboard from "@/components/patient/PatientDashboard";
import DoctorDashboard from "@/components/doctor/DoctorDashboard";

const Index = () => {
  const { state, dispatch } = useApp();

  // Initialize mock data on first load
  useEffect(() => {
    initializeMockData();
  }, []);

  const handleLogin = (email: string, password: string, userType: "patient" | "doctor") => {
    // Mock authentication - in production this would be Supabase auth
    const mockUsers = {
      doctor: { email: "<EMAIL>", password: "doctor123", name: "Dr. <PERSON>", id: "mock-doctor-id" },
      patient: { email: "<EMAIL>", password: "patient123", name: "<PERSON>", id: "mock-patient-id" }
    };

    const mockUser = mockUsers[userType];
    if (email === mockUser.email && password === mockUser.password) {
      const user = {
        type: userType,
        id: mockUser.id,
        name: mockUser.name
      };
      dispatch({ type: 'SET_USER', payload: user });
      return true;
    }
    return false;
  };

  const handleLogout = () => {
    dispatch({ type: 'SET_USER', payload: null });
  };

  if (!state.currentUser) {
    return <LoginForm onLogin={handleLogin} />;
  }

  if (state.currentUser.type === "patient") {
    return <PatientDashboard user={state.currentUser} onLogout={handleLogout} />;
  }

  if (state.currentUser.type === "doctor") {
    return <DoctorDashboard user={state.currentUser} onLogout={handleLogout} />;
  }

  return <LoginForm onLogin={handleLogin} />;
};

export default Index;
