import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useDoctor } from '@/hooks/useDoctor';
import { useApp } from '@/contexts/AppContext';
import { generateId } from '@/lib/utils';
import { 
  MessageSquare, 
  Send, 
  X, 
  User, 
  Clock,
  CheckCheck
} from 'lucide-react';
import type { Message } from '@/contexts/AppContext';

interface ChatSystemProps {
  isOpen: boolean;
  onClose: () => void;
  recipientId?: string;
  recipientName?: string;
  currentUserId: string;
  currentUserType: 'patient' | 'doctor';
}

const ChatSystem = ({ 
  isOpen, 
  onClose, 
  recipientId, 
  recipientName, 
  currentUserId, 
  currentUserType 
}: ChatSystemProps) => {
  const { state, dispatch } = useApp();
  const { sendMessage } = useDoctor();
  const [messageText, setMessageText] = useState('');
  const [selectedRecipient, setSelectedRecipient] = useState<string | null>(recipientId || null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get conversations
  const conversations = state.messages.reduce((acc, message) => {
    const otherUserId = message.senderId === currentUserId ? message.receiverId : message.senderId;
    if (!acc[otherUserId]) {
      acc[otherUserId] = [];
    }
    acc[otherUserId].push(message);
    return acc;
  }, {} as Record<string, Message[]>);

  // Get current conversation messages
  const currentMessages = selectedRecipient 
    ? (conversations[selectedRecipient] || []).sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
    : [];

  // Get available recipients
  const availableRecipients = currentUserType === 'doctor' 
    ? state.patients.filter(p => p.doctorId === currentUserId)
    : state.doctors.filter(d => d.patients.includes(currentUserId));

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentMessages]);

  useEffect(() => {
    if (recipientId) {
      setSelectedRecipient(recipientId);
    }
  }, [recipientId]);

  const handleSendMessage = () => {
    if (!messageText.trim() || !selectedRecipient) return;

    const message: Message = {
      id: generateId(),
      senderId: currentUserId,
      receiverId: selectedRecipient,
      content: messageText.trim(),
      timestamp: new Date().toISOString(),
      read: false
    };

    dispatch({ type: 'ADD_MESSAGE', payload: message });
    setMessageText('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const markMessagesAsRead = (messages: Message[]) => {
    messages.forEach(message => {
      if (message.receiverId === currentUserId && !message.read) {
        dispatch({ type: 'MARK_MESSAGE_READ', payload: message.id });
      }
    });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getRecipientName = (userId: string) => {
    const recipient = [...state.patients, ...state.doctors].find(u => u.id === userId);
    return recipient?.name || 'Unknown User';
  };

  const getUnreadCount = (userId: string) => {
    const messages = conversations[userId] || [];
    return messages.filter(m => m.receiverId === currentUserId && !m.read).length;
  };

  if (!isOpen) return null;

  return (
    <Card className="fixed top-20 right-4 w-96 h-[500px] z-50 shadow-lg flex flex-col">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {selectedRecipient ? `Chat with ${getRecipientName(selectedRecipient)}` : 'Messages'}
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {!selectedRecipient ? (
          // Conversation list
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-2">
              {availableRecipients.length > 0 ? (
                availableRecipients.map((recipient) => {
                  const unreadCount = getUnreadCount(recipient.id);
                  const lastMessage = conversations[recipient.id]?.slice(-1)[0];
                  
                  return (
                    <div
                      key={recipient.id}
                      className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => {
                        setSelectedRecipient(recipient.id);
                        markMessagesAsRead(conversations[recipient.id] || []);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-sm">{recipient.name}</p>
                            {lastMessage && (
                              <p className="text-xs text-gray-600 truncate">
                                {lastMessage.senderId === currentUserId ? 'You: ' : ''}
                                {lastMessage.content}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          {lastMessage && (
                            <span className="text-xs text-gray-400">
                              {formatTime(lastMessage.timestamp)}
                            </span>
                          )}
                          {unreadCount > 0 && (
                            <Badge className="h-5 w-5 rounded-full p-0 flex items-center justify-center bg-blue-500 text-white text-xs">
                              {unreadCount}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-sm">No conversations yet</p>
                  <p className="text-xs mt-1">
                    {currentUserType === 'doctor' 
                      ? 'Start chatting with your patients' 
                      : 'Your doctor will be able to chat with you'
                    }
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        ) : (
          // Chat interface
          <>
            <div className="px-4 py-2 border-b bg-gray-50">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSelectedRecipient(null)}
                className="text-blue-600 hover:text-blue-700"
              >
                ← Back to conversations
              </Button>
            </div>
            
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {currentMessages.length > 0 ? (
                  currentMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.senderId === currentUserId ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          message.senderId === currentUserId
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center gap-1 mt-1">
                          <Clock className="h-3 w-3 opacity-70" />
                          <span className="text-xs opacity-70">
                            {formatTime(message.timestamp)}
                          </span>
                          {message.senderId === currentUserId && message.read && (
                            <CheckCheck className="h-3 w-3 opacity-70" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No messages yet</p>
                    <p className="text-xs mt-1">Start the conversation!</p>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            <div className="p-4 border-t bg-white">
              <div className="flex gap-2">
                <Input
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  className="flex-1"
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={!messageText.trim()}
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ChatSystem;
