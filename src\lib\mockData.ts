import { generateId } from './utils';
import type { Patient, Doctor, Task } from '@/contexts/AppContext';

// Mock data initialization
export const initializeMockData = () => {
  // Check if data already exists
  const existingPatients = localStorage.getItem('patients');
  if (existingPatients && JSON.parse(existingPatients).length > 0) {
    return; // Data already exists
  }

  // Create mock doctor
  const mockDoctor: Doctor = {
    id: 'mock-doctor-id',
    name: 'Dr<PERSON> <PERSON>',
    email: '<EMAIL>',
    patients: []
  };

  // Create mock patient
  const mockPatient: Patient = {
    id: 'mock-patient-id',
    name: '<PERSON>',
    email: '<EMAIL>',
    condition: 'Post-Surgery Recovery',
    progress: 75,
    streak: 7,
    status: 'good',
    lastActive: new Date().toISOString(),
    totalPoints: 1250,
    completedTasks: 45,
    doctorId: mockDoctor.id
  };

  // Update doctor's patient list
  mockDoctor.patients = [mockPatient.id];

  // Create additional mock patients for the doctor
  const additionalPatients: Patient[] = [
    {
      id: generateId(),
      name: '<PERSON>',
      email: '<EMAIL>',
      condition: 'Cardiac Rehabilitation',
      progress: 92,
      streak: 12,
      status: 'excellent',
      lastActive: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
      totalPoints: 2100,
      completedTasks: 78,
      doctorId: mockDoctor.id
    },
    {
      id: generateId(),
      name: 'Mike Davis',
      email: '<EMAIL>',
      condition: 'Physical Therapy',
      progress: 45,
      streak: 2,
      status: 'needs-attention',
      lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      totalPoints: 680,
      completedTasks: 23,
      doctorId: mockDoctor.id
    },
    {
      id: generateId(),
      name: 'Emma Wilson',
      email: '<EMAIL>',
      condition: 'Diabetes Management',
      progress: 78,
      streak: 5,
      status: 'good',
      lastActive: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      totalPoints: 1450,
      completedTasks: 52,
      doctorId: mockDoctor.id
    }
  ];

  // Update doctor's patient list with additional patients
  mockDoctor.patients.push(...additionalPatients.map(p => p.id));

  // Create mock tasks for the main patient
  const mockTasks: Task[] = [
    {
      id: generateId(),
      title: 'Take morning medication',
      type: 'medication',
      completed: true,
      points: 50,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: '30-minute walk',
      type: 'exercise',
      completed: true,
      points: 75,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: 'Eat healthy lunch',
      type: 'diet',
      completed: false,
      points: 25,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString()
    },
    {
      id: generateId(),
      title: '8 hours of sleep',
      type: 'sleep',
      completed: false,
      points: 100,
      patientId: mockPatient.id,
      createdAt: new Date().toISOString()
    }
  ];

  // Create tasks for additional patients
  const additionalTasks: Task[] = [];
  additionalPatients.forEach(patient => {
    const patientTasks = [
      {
        id: generateId(),
        title: 'Take morning medication',
        type: 'medication' as const,
        completed: Math.random() > 0.3,
        points: 50,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Exercise routine',
        type: 'exercise' as const,
        completed: Math.random() > 0.4,
        points: 75,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Healthy meal',
        type: 'diet' as const,
        completed: Math.random() > 0.2,
        points: 25,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      },
      {
        id: generateId(),
        title: 'Rest and recovery',
        type: 'sleep' as const,
        completed: Math.random() > 0.5,
        points: 100,
        patientId: patient.id,
        createdAt: new Date().toISOString()
      }
    ];
    additionalTasks.push(...patientTasks);
  });

  // Save to localStorage
  localStorage.setItem('doctors', JSON.stringify([mockDoctor]));
  localStorage.setItem('patients', JSON.stringify([mockPatient, ...additionalPatients]));
  localStorage.setItem('tasks', JSON.stringify([...mockTasks, ...additionalTasks]));
  localStorage.setItem('notifications', JSON.stringify([]));
  localStorage.setItem('messages', JSON.stringify([]));
};
