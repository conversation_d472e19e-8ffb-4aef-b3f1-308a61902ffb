import { useEffect, useRef } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useToast } from '@/hooks/use-toast';

export const useRealTimeSync = () => {
  const { state, dispatch } = useApp();
  const { toast } = useToast();
  const lastNotificationCount = useRef(0);
  const lastTaskCount = useRef(0);

  // Monitor for new notifications and show toast
  useEffect(() => {
    const currentNotificationCount = state.notifications.length;
    
    if (currentNotificationCount > lastNotificationCount.current && lastNotificationCount.current > 0) {
      const newNotifications = state.notifications.slice(0, currentNotificationCount - lastNotificationCount.current);
      
      newNotifications.forEach(notification => {
        if (notification.userId === state.currentUser?.id) {
          toast({
            title: notification.title,
            description: notification.message,
            duration: 5000,
          });
        }
      });
    }
    
    lastNotificationCount.current = currentNotificationCount;
  }, [state.notifications, state.currentUser?.id, toast]);

  // Monitor for task completions and show achievements
  useEffect(() => {
    const currentTaskCount = state.tasks.filter(task => task.completed).length;
    
    if (currentTaskCount > lastTaskCount.current && lastTaskCount.current > 0) {
      const completedTasks = state.tasks.filter(task => task.completed);
      const newlyCompleted = completedTasks.slice(-(currentTaskCount - lastTaskCount.current));
      
      newlyCompleted.forEach(task => {
        if (task.patientId === state.currentUser?.id) {
          toast({
            title: "Task Completed! 🎉",
            description: `You earned ${task.points} points for completing "${task.title}"`,
            duration: 4000,
          });
        }
      });
    }
    
    lastTaskCount.current = currentTaskCount;
  }, [state.tasks, state.currentUser?.id, toast]);

  // Simulate real-time updates (in production, this would be WebSocket or Server-Sent Events)
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate random patient activity updates
      if (state.currentUser?.type === 'doctor' && state.patients.length > 0) {
        const randomPatient = state.patients[Math.floor(Math.random() * state.patients.length)];
        
        // Randomly update last active time for patients
        if (Math.random() < 0.1) { // 10% chance every 30 seconds
          dispatch({
            type: 'UPDATE_PATIENT',
            payload: {
              id: randomPatient.id,
              updates: {
                lastActive: new Date().toISOString()
              }
            }
          });
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [state.currentUser, state.patients, dispatch]);

  // Listen for browser tab visibility changes to sync data
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Tab became visible, trigger a data refresh
        // In a real app, this would fetch latest data from server
        console.log('Tab became visible, syncing data...');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // Provide methods for manual sync
  const syncData = () => {
    // In production, this would fetch latest data from server
    console.log('Manual data sync triggered');
  };

  const broadcastUpdate = (type: string, data: any) => {
    // In production, this would send updates to other connected clients
    console.log('Broadcasting update:', type, data);
  };

  return {
    syncData,
    broadcastUpdate,
    isOnline: navigator.onLine,
    lastSync: new Date().toISOString()
  };
};
