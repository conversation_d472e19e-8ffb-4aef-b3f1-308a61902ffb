import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { 
  downloadBackup, 
  uploadBackup, 
  importData, 
  clearAllData, 
  restoreFromBackup,
  exportData 
} from '@/lib/dataBackup';
import { 
  Download, 
  Upload, 
  Trash2, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Database,
  Shield
} from 'lucide-react';

interface DataManagementProps {
  onDataChange?: () => void;
}

const DataManagement = ({ onDataChange }: DataManagementProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  const handleExportData = () => {
    try {
      downloadBackup();
      toast({
        title: "Data Exported",
        description: "Your data has been downloaded as a backup file.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleImportData = async () => {
    setIsLoading(true);
    try {
      const backupData = await uploadBackup();
      const success = importData(backupData);
      
      if (success) {
        toast({
          title: "Data Imported",
          description: "Your backup has been successfully imported.",
        });
        onDataChange?.();
        // Reload the page to reflect changes
        window.location.reload();
      } else {
        throw new Error('Import failed');
      }
    } catch (error) {
      toast({
        title: "Import Failed",
        description: "Failed to import backup file. Please check the file format.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = () => {
    if (!showClearConfirm) {
      setShowClearConfirm(true);
      return;
    }

    setIsLoading(true);
    try {
      const success = clearAllData();
      if (success) {
        toast({
          title: "Data Cleared",
          description: "All data has been cleared. A backup was created before clearing.",
        });
        onDataChange?.();
        // Reload the page to reflect changes
        setTimeout(() => window.location.reload(), 1000);
      } else {
        throw new Error('Clear failed');
      }
    } catch (error) {
      toast({
        title: "Clear Failed",
        description: "Failed to clear data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setShowClearConfirm(false);
    }
  };

  const handleRestoreBackup = (backupKey: string) => {
    setIsLoading(true);
    try {
      const success = restoreFromBackup(backupKey);
      if (success) {
        toast({
          title: "Data Restored",
          description: "Your data has been restored from backup.",
        });
        onDataChange?.();
        // Reload the page to reflect changes
        setTimeout(() => window.location.reload(), 1000);
      } else {
        throw new Error('Restore failed');
      }
    } catch (error) {
      toast({
        title: "Restore Failed",
        description: "Failed to restore from backup. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getDataStats = () => {
    const data = exportData();
    return {
      patients: data.data.patients.length,
      tasks: data.data.tasks.length,
      notifications: data.data.notifications.length,
      messages: data.data.messages.length,
    };
  };

  const stats = getDataStats();

  return (
    <div className="space-y-6">
      {/* Data Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Overview
          </CardTitle>
          <CardDescription>
            Current data stored in your application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.patients}</div>
              <div className="text-sm text-gray-600">Patients</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.tasks}</div>
              <div className="text-sm text-gray-600">Tasks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.notifications}</div>
              <div className="text-sm text-gray-600">Notifications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.messages}</div>
              <div className="text-sm text-gray-600">Messages</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Backup & Restore */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Backup & Restore
          </CardTitle>
          <CardDescription>
            Export your data for backup or import from a previous backup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleExportData}
              className="flex-1"
              disabled={isLoading}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button 
              onClick={handleImportData}
              variant="outline"
              className="flex-1"
              disabled={isLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import Data
            </Button>
          </div>

          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Auto-backups are created every 30 minutes and stored locally. 
              Export your data regularly for additional safety.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Quick Restore */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Restore</CardTitle>
          <CardDescription>
            Restore from recent automatic backups
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            onClick={() => handleRestoreBackup('backup_before_clear')}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Restore Before Last Clear
          </Button>
          <Button 
            onClick={() => handleRestoreBackup('backup_before_import')}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Restore Before Last Import
          </Button>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Danger Zone
          </CardTitle>
          <CardDescription>
            Irreversible actions that will affect your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!showClearConfirm ? (
            <Button 
              onClick={handleClearData}
              variant="destructive"
              disabled={isLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All Data
            </Button>
          ) : (
            <div className="space-y-3">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Are you sure? This will permanently delete all your data. 
                  A backup will be created before clearing.
                </AlertDescription>
              </Alert>
              <div className="flex gap-2">
                <Button 
                  onClick={handleClearData}
                  variant="destructive"
                  disabled={isLoading}
                >
                  Yes, Clear All Data
                </Button>
                <Button 
                  onClick={() => setShowClearConfirm(false)}
                  variant="outline"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DataManagement;
