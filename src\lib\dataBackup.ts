import { storage } from './utils';
import type { AppState } from '@/contexts/AppContext';

export interface BackupData {
  version: string;
  timestamp: string;
  data: {
    patients: any[];
    doctors: any[];
    tasks: any[];
    notifications: any[];
    messages: any[];
  };
}

export const exportData = (): BackupData => {
  const data = {
    patients: storage.get('patients', []),
    doctors: storage.get('doctors', []),
    tasks: storage.get('tasks', []),
    notifications: storage.get('notifications', []),
    messages: storage.get('messages', []),
  };

  return {
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    data
  };
};

export const importData = (backupData: BackupData): boolean => {
  try {
    // Validate backup data structure
    if (!backupData.data || typeof backupData.data !== 'object') {
      throw new Error('Invalid backup data structure');
    }

    const { patients, doctors, tasks, notifications, messages } = backupData.data;

    // Validate required arrays
    if (!Array.isArray(patients) || !Array.isArray(doctors) || !Array.isArray(tasks)) {
      throw new Error('Invalid backup data: missing required arrays');
    }

    // Create backup of current data before importing
    const currentBackup = exportData();
    storage.set('backup_before_import', currentBackup);

    // Import new data
    storage.set('patients', patients);
    storage.set('doctors', doctors);
    storage.set('tasks', tasks);
    storage.set('notifications', notifications || []);
    storage.set('messages', messages || []);

    return true;
  } catch (error) {
    console.error('Failed to import data:', error);
    return false;
  }
};

export const downloadBackup = () => {
  const backup = exportData();
  const dataStr = JSON.stringify(backup, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  
  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `healhero-backup-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const uploadBackup = (): Promise<BackupData> => {
  return new Promise((resolve, reject) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) {
        reject(new Error('No file selected'));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const backupData = JSON.parse(e.target?.result as string);
          resolve(backupData);
        } catch (error) {
          reject(new Error('Invalid JSON file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    };
    
    input.click();
  });
};

export const clearAllData = (): boolean => {
  try {
    // Create backup before clearing
    const backup = exportData();
    storage.set('backup_before_clear', backup);

    // Clear all data
    storage.remove('patients');
    storage.remove('doctors');
    storage.remove('tasks');
    storage.remove('notifications');
    storage.remove('messages');

    return true;
  } catch (error) {
    console.error('Failed to clear data:', error);
    return false;
  }
};

export const restoreFromBackup = (backupKey: string): boolean => {
  try {
    const backup = storage.get(backupKey, null);
    if (!backup) {
      throw new Error('Backup not found');
    }

    return importData(backup);
  } catch (error) {
    console.error('Failed to restore from backup:', error);
    return false;
  }
};

// Auto-backup functionality
export const createAutoBackup = () => {
  const backup = exportData();
  const backups = storage.get('auto_backups', []);
  
  // Keep only last 5 auto-backups
  const newBackups = [backup, ...backups].slice(0, 5);
  storage.set('auto_backups', newBackups);
};

// Initialize auto-backup on app start
export const initializeAutoBackup = () => {
  // Create initial backup
  createAutoBackup();
  
  // Set up periodic backups (every 30 minutes)
  setInterval(createAutoBackup, 30 * 60 * 1000);
};
