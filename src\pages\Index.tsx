
import { useState } from "react";
import LoginForm from "@/components/auth/LoginForm";
import PatientDashboard from "@/components/patient/PatientDashboard";
import DoctorDashboard from "@/components/doctor/DoctorDashboard";

const Index = () => {
  const [user, setUser] = useState<{type: "patient" | "doctor", id: string, name: string} | null>(null);

  const handleLogin = (email: string, password: string, userType: "patient" | "doctor") => {
    // Mock authentication - in production this would be Supabase auth
    const mockUsers = {
      doctor: { email: "<EMAIL>", password: "doctor123", name: "Dr<PERSON> <PERSON>" },
      patient: { email: "<EMAIL>", password: "patient123", name: "<PERSON>" }
    };

    const mockUser = mockUsers[userType];
    if (email === mockUser.email && password === mockUser.password) {
      setUser({ 
        type: userType, 
        id: `mock-${userType}-id`,
        name: mockUser.name
      });
      return true;
    }
    return false;
  };

  const handleLogout = () => {
    setUser(null);
  };

  if (!user) {
    return <LoginForm onLogin={handleLogin} />;
  }

  if (user.type === "patient") {
    return <PatientDashboard user={user} onLogout={handleLogout} />;
  }

  if (user.type === "doctor") {
    return <DoctorDashboard user={user} onLogout={handleLogout} />;
  }

  return <LoginForm onLogin={handleLogin} />;
};

export default Index;
